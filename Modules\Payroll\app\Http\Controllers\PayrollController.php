<?php

namespace Modules\Payroll\App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PayrollRecord;
use App\Models\Payslip;
use App\Models\Settings;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class PayrollController extends Controller
{
    public function index()
    {
        $employees = User::all();
        return view('payroll::index', compact('employees'));
    }

    public function getListAjax(Request $request)
    {
        $query = PayrollRecord::with('user')
            ->when($request->has('dateFilter') && !empty($request->dateFilter), function ($query) use ($request) {
                $query->where('period', Carbon::parse($request->dateFilter)->format('F Y'));
            })->when($request->has('employeeFilter') && !empty($request->employeeFilter), function ($query) use ($request) {
                $query->where('user_id', $request->employeeFilter);
            })->when($request->has('statusFilter') && !empty($request->statusFilter), function ($query) use ($request) {
                $query->where('status', $request->statusFilter);
            });

        $currencySymbol = Settings::first()->currency_symbol;

        return DataTables::of($query)
            ->addColumn('user', function ($record) {
                return view('_partials._profile-avatar', [
                    'user' => $record->user,
                ])->render();
            })
            ->addColumn('period', fn($record) => $record->period)
            ->addColumn('status', fn($record) => ucfirst($record->status))
            ->editColumn('basic_salary', fn($record) => $currencySymbol . number_format($record->basic_salary, 2))
            ->editColumn('gross_salary', fn($record) => $currencySymbol . number_format($record->gross_salary, 2))
            ->editColumn('net_salary', fn($record) => $currencySymbol . number_format($record->net_salary, 2))
            ->addColumn('actions', fn($record) => view('_partials._action-icons', [
                'show' => route('payroll.show', $record->id),
            ]))
            ->editColumn('status', function ($record) {
                $html = '';
                if ($record->status == 'pending') {
                    $html = '<span class="badge bg-warning">Pending</span>';
                } elseif ($record->status == 'completed' || $record->status == 'paid') {
                    $html = '<span class="badge bg-success">' . ucfirst($record->status) . '</span>';
                } else {
                    $html = '<span class="badge bg-danger">Cancelled</span>';
                }
                return $html;
            })
            ->rawColumns(['actions', 'status', 'user'])
            ->make(true);
    }

    public function create()
    {
        $period = Carbon::now()->format('F Y');

        //Check if the payroll has already been processed
        $payroll = PayrollRecord::where('period', $period)->first();
        if ($payroll) {
            return redirect()->route('payroll.index')->with('error', 'Payroll has already been processed for this period');
        }

        //php artisan payroll:process --period="January 2025"
        Artisan::call('payroll:process', ['--period' => $period]);

        return redirect()->route('payroll.index')->with('success', 'Payroll has been processed successfully');
    }

    public function show($id)
    {
        $payroll = PayrollRecord::with(['user', 'payrollCycle', 'payrollAdjustments', 'payrollAdjustmentLogs'])->findOrFail($id);
        $settings = Settings::first();
        return view('payroll::show', [
            'payroll' => $payroll,
            'settings' => $settings
        ]);
    }

    public function getPayslip($id)
    {
        $payslip = Payslip::with('user')->findOrFail($id);
        return view('payroll::payslip', compact('payslip'));
    }

    /**
     * Helper method to get logo for PDF generation with better error handling
     */
    private function getLogoForPDF($settings)
    {
        $logoData = [
            'base64' => null,
            'exists' => false
        ];

        if (!$settings->company_logo) {
            return $logoData;
        }

        try {
            // Check if file exists in storage
            if (Storage::disk('public')->exists('images/' . $settings->company_logo)) {
                // Get the file content
                $fileContent = Storage::disk('public')->get('images/' . $settings->company_logo);
                
                if ($fileContent) {
                    // Determine MIME type
                    $extension = strtolower(pathinfo($settings->company_logo, PATHINFO_EXTENSION));
                    $mimeType = match($extension) {
                        'jpg', 'jpeg' => 'image/jpeg',
                        'png' => 'image/png',
                        'gif' => 'image/gif',
                        'webp' => 'image/webp',
                        'bmp' => 'image/bmp',
                        'svg' => 'image/svg+xml',
                        default => 'image/jpeg'
                    };

                    // Create base64 string
                    $logoData['base64'] = 'data:' . $mimeType . ';base64,' . base64_encode($fileContent);
                    $logoData['exists'] = true;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to process company logo: ' . $e->getMessage());
        }

        return $logoData;
    }

    public function generatePayslipPDF($payslipId)
    {
        try {
            // Fetch Payslip Details
            $payslip = Payslip::with(['user.designation', 'payrollRecord.payrollCycle', 'payrollRecord.payrollAdjustmentLogs'])
                ->findOrFail($payslipId);

            $settings = Settings::first();

            // Get logo data using helper method
            $logoData = $this->getLogoForPDF($settings);

            // Company Details
            $companyDetails = [
                'name' => $settings->company_name,
                'address' => $settings->company_address,
                'phone' => $settings->company_phone,
                'email' => $settings->company_email,
                'logoBase64' => $logoData['base64'],
                'logoExists' => $logoData['exists']
            ];

            // Prepare Data for PDF
            $data = [
                'currencySymbol' => $settings->currency_symbol,
                'payslip' => $payslip,
                'company' => $companyDetails,
            ];

            // Load PDF View with better error handling
            $pdf = Pdf::loadView('payslip.pdf', $data)
                ->setPaper('a4', 'portrait')
                ->setOptions([
                    'isHtml5ParserEnabled' => true,
                    'isPhpEnabled' => true,
                    'chroot' => storage_path('app/public'),
                    'isRemoteEnabled' => true,
                ]);

            // Optionally Save PDF on Server
            $fileName = 'payslips/' . $payslip->user->first_name . '-' . $payslip->user->last_name . '-payslip-' . now()->format('Y-m-d') . '.pdf';
            Storage::put('public/' . $fileName, $pdf->output());

            // Download PDF
            return $pdf->download('payslip-' . $payslip->user->getFullName() . '-' . $payslip->payrollRecord->period . '.pdf');

        } catch (\Exception $e) {
            \Log::error('PDF generation failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to generate PDF. Please try again.');
        }
    }
}