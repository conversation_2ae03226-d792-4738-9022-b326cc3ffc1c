<?php

namespace Modules\Payroll\Console;

use App\Enums\LeaveRequestStatus;
use App\Models\LeaveRequest;
use App\Models\PayrollAdjustment;
use App\Models\PayrollAdjustmentLog;
use App\Models\PayrollCycle;
use App\Models\PayrollRecord;
use App\Models\Payslip;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessPayrollCommand extends Command
{
  protected $signature = 'payroll:process {--period= : The payroll period (e.g., "January 2025")}';
  protected $description = 'Process payroll for a specific period, including global and employee-specific adjustments';

  public function handle()
  {
    try {
      $this->info('Starting payroll processing...');
      Log::info('Starting payroll processing...');
      $period = $this->option('period') ?? Carbon::now()->format('F Y');

      $this->info("Processing payroll for period: $period");
      Log::info("Processing payroll for period: $period");

      // Create or Retrieve Payroll Cycle
      $payrollCycle = PayrollCycle::firstOrCreate(
        ['code' => Str::slug($period)],
        [
          'name' => $period . ' Payroll',
          'frequency' => 'monthly',
          'pay_period_start' => Carbon::parse('first day of ' . $period),
          'pay_period_end' => Carbon::parse('last day of ' . $period),
          'pay_date' => Carbon::parse('last day of ' . $period)->addDays(5),
          'status' => 'pending',
        ]
      );

      $this->info("Payroll cycle created/updated: {$payrollCycle->name}");
      Log::info("Payroll cycle created/updated: {$payrollCycle->name}");

      // Fetch all active users
      $users = User::where('status', 'active')->get();

      foreach ($users as $user) {
        DB::beginTransaction();
        try {

          //Get Approved Leaves
          $approvedLeaves = LeaveRequest::where('status', LeaveRequestStatus::APPROVED)
            ->whereBetween('from_date', [$payrollCycle->pay_period_start, $payrollCycle->pay_period_end])
            ->whereBetween('to_date', [$payrollCycle->pay_period_start, $payrollCycle->pay_period_end])
            ->get();

          //Get leave count using from and to date
          $leaveCount = 0;
          foreach ($approvedLeaves as $leave) {
            $leaveCount += $leave->from_date->diffInDays($leave->to_date) + 1;
          }


          // Attendance-based Calculations
          $workingHours = $user->attendances()
            ->whereBetween('check_in_time', [$payrollCycle->pay_period_start, $payrollCycle->pay_period_end])
            ->sum('working_hours');

          $overtimeHours = $user->attendances()
            ->whereBetween('check_in_time', [$payrollCycle->pay_period_start, $payrollCycle->pay_period_end])
            ->sum('overtime_hours');

          $absentDays = $user->attendances()
            ->whereBetween('created_at', [$payrollCycle->pay_period_start, $payrollCycle->pay_period_end])
            ->where('status', 'absent')
            ->count();

          $basicSalary = $user->base_salary ?? 0;
          $overtimePay = $overtimeHours * $user->overtime_rate;
          $grossSalary = $basicSalary + $overtimePay;
          $netSalary = $grossSalary;

          // Fetch Adjustments (Global + Employee-Specific)
          $adjustments = PayrollAdjustment::where(function ($query) use ($user) {
            $query->where('applicability', 'global')
              ->orWhere('user_id', $user->id);
          })->get();

          $totalDeductions = 0;
          $totalBenefits = 0;

          $adjustmentsLogs = [];
          // Process Adjustments
          foreach ($adjustments as $adjustment) {
            $amount = $adjustment->amount;
            if ($adjustment->percentage) {
              $amount = ($grossSalary * $adjustment->percentage) / 100;
            }

            if ($adjustment->type === 'deduction') {
              $netSalary -= $amount;
              $totalDeductions += $amount;
            } elseif ($adjustment->type === 'benefit') {
              $netSalary += $amount;
              $totalBenefits += $amount;
            }

            $adjustmentsLogs[] = [
              'payroll_adjustment_id' => $adjustment->id,
              'name' => $adjustment->name,
              'type' => $adjustment->type,
              'applicability' => $adjustment->applicability,
              'amount' => $amount,
              'percentage' => $adjustment->percentage,
              'user_id' => $adjustment->user_id,
              'log_message' => "Applied adjustment during payroll processing.",
            ];
          }

          // Create Payroll Record
          $payrollRecord = PayrollRecord::create([
            'user_id' => $user->id,
            'payroll_cycle_id' => $payrollCycle->id,
            'period' => $period,
            'basic_salary' => $basicSalary,
            'gross_salary' => $grossSalary,
            'net_salary' => $netSalary,
            'tax_amount' => 0, // Tax calculation can be added later
            'status' => 'pending',
          ]);

          //Insert all logs for adjustments with the payroll record id
          PayrollAdjustmentLog::insert(array_map(function ($log) use ($payrollRecord) {
            $log['payroll_record_id'] = $payrollRecord->id;
            return $log;
          }, $adjustmentsLogs));


          // Create Payslip
          Payslip::create([
            'user_id' => $user->id,
            'payroll_record_id' => $payrollRecord->id,
            'code' => 'PSL-' . uniqid(),
            'basic_salary' => $basicSalary,
            'total_deductions' => $totalDeductions,
            'total_benefits' => $totalBenefits,
            'net_salary' => $netSalary,
            'status' => 'generated',
            'total_worked_days' => $workingHours / 8,
            //TODO: Add other attendance-based calculations
            'total_absent_days' => $absentDays,
            'total_leave_days' => $leaveCount,
          ]);

          $this->info("Processed payroll for {$user->first_name} {$user->last_name}");
          $this->info("Basic Salary: $basicSalary, Gross Salary: $grossSalary, Net Salary: $netSalary");
          Log::info("Processed payroll for {$user->first_name} {$user->last_name}");
          Log::info("Basic Salary: $basicSalary, Gross Salary: $grossSalary, Net Salary: $netSalary");
          DB::commit();
        } catch (Exception $e) {
          DB::rollBack();
          $this->error("Failed to process payroll for {$user->first_name} {$user->last_name}: {$e->getMessage()}");
          Log::info("Failed to process payroll for {$user->first_name} {$user->last_name}: {$e->getMessage()}");
          Log::info($e->getMessage());
        }
      }

      // Update Payroll Cycle Status
      $payrollCycle->status = 'processed';
      $payrollCycle->save();

      $this->info("Payroll processing completed for period: $period");
      Log::info("Payroll processing completed for period: $period");
    } catch (Exception $e) {
      $this->error("An error occurred: {$e->getMessage()}");
      Log::error($e->getMessage());
    }

  }

}
