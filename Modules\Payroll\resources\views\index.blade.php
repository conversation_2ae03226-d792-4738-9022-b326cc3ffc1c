@extends('layouts.layoutMaster')

@section('title', __('Payroll Management'))

@section('vendor-style')
  @vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'])
@endsection

@section('vendor-script')
  @vite(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection

@section('page-script')
  @vite(['resources/assets/js/app/payroll-index.js'])
@endsection

@section('content')
  <div class="row">
    <div class="col">
      <h4>@lang('Payroll Management')</h4>
    </div>
    <div class="col">
      <div class="float-end">
        <a href="{{route('payroll.create')}}" class="btn btn-primary">@lang('Generate')</a>
      </div>
    </div>
  </div>
  <!-- Filters Section -->
  <div class="row mb-4">
    <!-- Employee Filter -->
    <div class="col-md-3 mb-3">
      <label for="employeeFilter" class="form-label">Filter by employee</label>
      <select id="employeeFilter" name="employeeFilter" class="form-select select2 filter-input">
        <option value="" selected>All Employees</option>
        @foreach($employees as $employee)
          <option value="{{ $employee->id }}">{{ $employee->first_name }} {{ $employee->last_name }}</option>
        @endforeach
      </select>
    </div>

    <!-- Date Filter -->
    <div class="col-md-3 mb-3">
      <label for="dateFilter" class="form-label">Filter by Period</label>
      <input type="month" id="dateFilter" name="dateFilter" class="form-control filter-input"
             value="{{ date('Y-m') }}">
    </div>

    <!-- Status Filter -->
    <div class="col-md-3 mb-3">
      <label for="statusFilter" class="form-label">Filter by Status</label>
      <select id="statusFilter" name="statusFilter" class="form-select select2 filter-input">
        <option value="" selected>All Status</option>
        <option value="pending">Pending</option>
        <option value="completed">Completed</option>
        <option value="paid">Paid</option>
        <option value="cancelled">Cancelled</option>
      </select>
    </div>
  </div>

  <!-- Payroll Table -->
  <div class="card">
    <div class="card-datatable table-responsive">
      <table class="table border-top datatables-payroll">
        <thead>
        <tr>
          <th>#</th>
          <th>User</th>
          <th>Period</th>
          <th>Basic Salary</th>
          <th>Gross Salary</th>
          <th>Net Salary</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>
@endsection
