<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payslip</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 15px;
      font-size: 11px;
      line-height: 1.3;
    }

    .payslip-container {
      border: 2px solid #000;
      max-width: 800px;
      margin: 0 auto;
    }

    .header-section {
      border-bottom: 1px solid #000;
      padding: 10px;
      display: table;
      width: 100%;
    }

    .company-info {
      display: table-cell;
      vertical-align: top;
      width: 70%;
    }

    .company-logo {
      display: table-cell;
      vertical-align: top;
      width: 30%;
      text-align: right;
    }

    .company-logo img {
      max-height: 80px;
      max-width: 150px;
    }

    .company-name {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .company-address {
      font-size: 10px;
      line-height: 1.2;
      margin-bottom: 10px;
    }

    .payslip-title {
      background-color: #000;
      color: white;
      text-align: center;
      padding: 8px;
      font-weight: bold;
      font-size: 12px;
    }

    .employee-summary {
      background-color: #f0f0f0;
      padding: 5px 10px;
      font-weight: bold;
      border-bottom: 1px solid #000;
    }

    .employee-details {
      display: table;
      width: 100%;
      border-bottom: 1px solid #000;
    }

    .employee-left, .employee-right {
      display: table-cell;
      vertical-align: top;
      padding: 8px;
      width: 50%;
    }

    .employee-right {
      border-left: 1px solid #000;
    }

    .detail-row {
      margin-bottom: 3px;
    }

    .detail-label {
      display: inline-block;
      width: 120px;
      font-weight: normal;
    }

    .detail-value {
      font-weight: bold;
    }

    .pay-details {
      display: table;
      width: 100%;
    }

    .earnings-section, .deductions-section {
      display: table-cell;
      vertical-align: top;
      width: 50%;
      border-bottom: 1px solid #000;
    }

    .deductions-section {
      border-left: 1px solid #000;
    }

    .section-header {
      background-color: #f0f0f0;
      text-align: center;
      font-weight: bold;
      padding: 5px;
      border-bottom: 1px solid #000;
    }

    .pay-item {
      display: table;
      width: 100%;
      border-bottom: 1px solid #ccc;
    }

    .pay-item-name, .pay-item-amount {
      display: table-cell;
      padding: 4px 8px;
      vertical-align: middle;
    }

    .pay-item-name {
      width: 70%;
    }

    .pay-item-amount {
      width: 30%;
      text-align: right;
      font-weight: bold;
    }

    .total-row {
      background-color: #f0f0f0;
      font-weight: bold;
      border-top: 2px solid #000;
    }

    .net-pay {
      background-color: #000;
      color: white;
      text-align: center;
      padding: 8px;
      font-weight: bold;
      font-size: 12px;
    }

    .amount-words {
      padding: 8px;
      font-style: italic;
      border-bottom: 1px solid #000;
    }

    .signature-section {
      padding: 15px;
      text-align: right;
    }

    .signature-line {
      border-bottom: 1px solid #000;
      width: 200px;
      margin-left: auto;
      margin-top: 30px;
      text-align: center;
      padding-top: 5px;
    }

    @media print {
      body { margin: 0; }
      .payslip-container { border: 2px solid #000; }
    }
  </style>
</head>
<body>
<div class="payslip-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="company-info">
      <div class="company-name">{{ $company['name'] }}</div>
      <div class="company-address">
        {{ $company['address'] }}<br>
        {{ $company['phone'] }} | {{ $company['email'] }}
      </div>
    </div>
    <div class="company-logo">
      @if(isset($company['logoBase64']) && $company['logoBase64'])
        <img src="{{ $company['logoBase64'] }}" alt="Company Logo" style="max-height: 80px; max-width: 150px;">
      @else
        <div style="width: 150px; height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #999;">
          No Logo
        </div>
      @endif
    </div>
  </div>

  <!-- Payslip Title -->
  <div class="payslip-title">
    Payslip for the Month of {{ $payslip->payrollRecord->period }}
  </div>

  <!-- Employee Pay Summary -->
  <div class="employee-summary">
    Employee Pay Summary
  </div>

  <!-- Employee Details -->
  <div class="employee-details">
    <div class="employee-left">
      <div class="detail-row">
        <span class="detail-label">Employee Name</span>
        <span class="detail-value">: {{ $payslip->user->getFullName() }}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Designation</span>
        <span class="detail-value">: {{ $payslip->user->designation->name ?? 'N/A' }}</span>
      </div>
    </div>
    <div class="employee-right">
      <div class="detail-row">
        <span class="detail-label">Date of Joining</span>
        <span class="detail-value">: {{ $payslip->user->date_of_joining ? $payslip->user->date_of_joining->format('d-m-Y') : 'N/A' }}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Pay Date</span>
        <span class="detail-value">: {{ $payslip->payrollRecord->payrollCycle->pay_date ? $payslip->payrollRecord->payrollCycle->pay_date->format('d-m-Y') : date('d-m-Y') }}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">UAN Number</span>
        <span class="detail-value">: {{ $payslip->user->uan_number ?? 'N/A' }}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">PAN Number</span>
        <span class="detail-value">: {{ $payslip->user->pan_number ?? 'N/A' }}</span>
      </div>
    </div>
  </div>

  <!-- Pay Details Header -->
  <div style="display: table; width: 100%;">
    <div style="display: table-cell; width: 33.33%; text-align: center; padding: 5px; border-right: 1px solid #000; font-weight: bold;">
      Paid Days: {{ $payslip->total_worked_days ?? 0 }}
    </div>
    <div style="display: table-cell; width: 33.33%; text-align: center; padding: 5px; border-right: 1px solid #000; font-weight: bold;">
      LOP Days: {{ $payslip->total_absent_days ?? 0 }}
    </div>
    <div style="display: table-cell; width: 33.33%; text-align: center; padding: 5px; font-weight: bold;">
      Total Working Days: {{ $payslip->total_working_days ?? 30 }}
    </div>
  </div>

  <!-- Earnings and Deductions -->
  <div class="pay-details">
    <!-- Earnings Section -->
    <div class="earnings-section">
      <div class="section-header">EARNINGS</div>

      <div class="pay-item">
        <div class="pay-item-name">Basic</div>
        <div class="pay-item-amount">{{ number_format($payslip->basic_salary, 0) }}</div>
      </div>

      @php
        $earnings = $payslip->payrollRecord->payrollAdjustmentLogs->where('type', 'benefit') ?? collect();
        $totalEarnings = $payslip->basic_salary;
      @endphp

      @foreach($earnings as $earning)
        @php $totalEarnings += $earning->amount; @endphp
        <div class="pay-item">
          <div class="pay-item-name">{{ $earning->name }}</div>
          <div class="pay-item-amount">{{ number_format($earning->amount, 0) }}</div>
        </div>
      @endforeach

      <div class="pay-item total-row">
        <div class="pay-item-name">Gross Earnings</div>
        <div class="pay-item-amount">{{ number_format($totalEarnings, 0) }}</div>
      </div>
    </div>

    <!-- Deductions Section -->
    <div class="deductions-section">
      <div class="section-header">DEDUCTIONS</div>

      @php
        $deductions = $payslip->payrollRecord->payrollAdjustmentLogs->where('type', 'deduction') ?? collect();
        $totalDeductions = 0;
      @endphp

      @if($deductions->count() > 0)
        @foreach($deductions as $deduction)
          @php $totalDeductions += $deduction->amount; @endphp
          <div class="pay-item">
            <div class="pay-item-name">{{ $deduction->name }}</div>
            <div class="pay-item-amount">{{ number_format($deduction->amount, 0) }}</div>
          </div>
        @endforeach
      @else
        <div class="pay-item">
          <div class="pay-item-name">P.F.</div>
          <div class="pay-item-amount">{{ number_format($payslip->total_deductions * 0.6, 0) }}</div>
        </div>
        <div class="pay-item">
          <div class="pay-item-name">ESIC</div>
          <div class="pay-item-amount">-</div>
        </div>
        <div class="pay-item">
          <div class="pay-item-name">Professional Tax</div>
          <div class="pay-item-amount">{{ number_format($payslip->total_deductions * 0.4, 0) }}</div>
        </div>
        @php $totalDeductions = $payslip->total_deductions; @endphp
      @endif

      <div class="pay-item total-row">
        <div class="pay-item-name">Total Deductions</div>
        <div class="pay-item-amount">{{ number_format($totalDeductions, 0) }}</div>
      </div>
    </div>
  </div>

  <!-- Net Pay -->
  <div class="net-pay">
    Total Net Payable: {{ number_format($payslip->net_salary, 0) }}
  </div>

  <!-- Amount in Words -->
  <div class="amount-words">
    @php
      $amountInWords = \App\Helpers\NumberToWords::convert($payslip->net_salary);
    @endphp
    {{ ucfirst($amountInWords) }} Only
  </div>

  <!-- Signature Section -->
  <!-- <div class="signature-section">
    <div style="text-align: right; margin-top: 20px;">
      For {{ $company['name'] }}
      <div class="signature-line">
        Authorised Signatory
      </div>
    </div>
  </div> -->
  <!-- Amount in Words -->
  

  <!-- Footer Section -->
  <div class="footer-section">
    <!-- Auto Generated Notice -->
    <div class="auto-generated">
      <div class="auto-generated-title">Computer Generated Payslip</div>
      <div class="auto-generated-text">
        This payslip has been automatically generated by our payroll system. No physical signature is required for authenticity. 
        This document is valid for all official purposes and serves as proof of salary payment.
      </div>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
      <div style="margin-bottom: 10px;">For {{ $company['name'] }}</div>
      <!-- <div class="company-stamp">
        Digital Payroll System
      </div> -->
      <div style="margin-top: 5px; font-size: 10px;">
        Generated on: {{ date('d-m-Y H:i:s') }}
      </div>
    </div>
  </div>
</div>
</body>
</html>