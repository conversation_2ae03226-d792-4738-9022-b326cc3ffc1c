@extends('layouts.layoutMaster')

@section('title', __('Payroll Details'))

@section('vendor-style')
  @vite(['resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'])
@endsection

@section('vendor-script')
  @vite(['resources/assets/vendor/libs/sweetalert2/sweetalert2.js'])
@endsection

@section('content')
  <div class="row mt-3">
    <!-- Payroll Details (8 Columns) -->
    <div class="col-md-8" id="printableArea">
      <div class="card shadow-sm">
        <!-- Card Header with Actions -->
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="bx bx-money me-2"></i>@lang('Payroll Details')</h5>
          {{-- <div>
             <a href="#" onclick="sendPayroll()" class="btn btn-sm btn-outline-primary me-2">
               <i class="bx bx-send"></i> @lang('Send')
             </a>
             <button onclick="window.print()" class="btn btn-sm btn-outline-secondary">
               <i class="bx bx-printer"></i> @lang('Print')
             </button>
           </div>--}}
        </div>

        <!-- Card Body -->
        <div class="card-body">
          <!-- User Info Section -->
          <h6 class="fw-bold text-primary mb-3"><i class="bx bx-user me-1"></i>@lang('User Information')</h6>
          <ul class="list-unstyled mb-4">
            <li><strong>@lang('User:')</strong> {{ $payroll->user->first_name }} {{ $payroll->user->last_name }}</li>
            <li><strong>@lang('Email:')</strong> {{ $payroll->user->email }}</li>
            <li><strong>@lang('Employee Code:')</strong> {{ $payroll->user->code }}</li>
            <li><strong>@lang('Designation:')</strong> {{ $payroll->user->designation->name ?? 'N/A' }}</li>
            <li><strong>@lang('Phone:')</strong> {{ $payroll->user->phone }}</li>
          </ul>

          <!-- Payroll Cycle Section -->
          <h6 class="fw-bold text-primary mb-3"><i class="bx bx-calendar me-1"></i>@lang('Payroll Cycle')</h6>
          <ul class="list-unstyled mb-4">
            <li><strong>@lang('Cycle Name:')</strong> {{ $payroll->payrollCycle->name }}</li>
            <li>
              <strong>@lang('Pay Period:')</strong> {{ $payroll->payrollCycle->pay_period_start->format(Constants::DateFormat) }}
              - {{ $payroll->payrollCycle->pay_period_end->format(Constants::DateFormat) }}</li>
            <li>
              <strong>@lang('Pay Date:')</strong> {{ $payroll->payrollCycle->pay_date->format(Constants::DateFormat) }}
            </li>
            <li><strong>@lang('Status:')</strong>
              @if($payroll->status === 'paid')
                <span class="badge bg-success">@lang('Paid')</span>
              @elseif($payroll->status === 'pending')
                <span class="badge bg-warning">@lang('Pending')</span>
              @else
                <span class="badge bg-secondary">{{ ucfirst($payroll->status) }}</span>
              @endif
            </li>
          </ul>

          <!-- Salary Details Section -->
          <h6 class="fw-bold text-primary mb-3"><i class="bx bx-wallet me-1"></i>@lang('Salary Details')</h6>
          <div class="table-responsive">
            <table class="table table-bordered mb-4">
              <thead>
              <tr>
                <th>@lang('Payable CTC')</th>
                <th>@lang('Gross Salary')</th>
                <th>@lang('Net Salary')</th>
                <th>@lang('Tax Amount')</th>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td>{{$settings->currency_symbol}}{{ number_format($payroll->basic_salary, 2) }}</td>
                <td>{{$settings->currency_symbol}}{{ number_format($payroll->gross_salary, 2) }}</td>
                <td>{{$settings->currency_symbol}}{{ number_format($payroll->net_salary, 2) }}</td>
                <td>{{$settings->currency_symbol}}{{ number_format($payroll->tax_amount, 2) }}</td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- Adjustments Section -->
          <h6 class="fw-bold text-primary mb-3"><i class="bx bx-adjust me-1"></i>@lang('Adjustments')</h6>
          @if($payroll->payrollAdjustmentLogs->count() > 0)
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                <tr>
                  <th>@lang('Name')</th>
                  <th>@lang('Type')</th>
                  <th>@lang('Amount')</th>
                  <th>@lang('Percentage')</th>
                </tr>
                </thead>
                <tbody>
                @foreach($payroll->payrollAdjustmentLogs as $adjustment)
                  <tr>
                    <td>{{ $adjustment->name }}</td>
                    <td>{{ ucfirst($adjustment->type) }}</td>
                    <td>{{$settings->currency_symbol}}{{ number_format($adjustment->amount, 2) }}</td>
                    <td>{{ $adjustment->percentage ?? '-' }}%</td>
                  </tr>
                @endforeach
                </tbody>
              </table>
            </div>
          @else
            <p class="text-muted">@lang('No adjustments found')</p>
          @endif
        </div>
      </div>
    </div>

    <!-- Meta Info & Actions (4 Columns) -->
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header">
          <h6 class="mb-0"><i class="bx bx-info-circle me-1"></i>@lang('Information')</h6>
        </div>
        <div class="card-body">
          <ul class="list-unstyled">
            <li><strong>@lang('Payroll ID:')</strong> {{ $payroll->id }}</li>
            <li><strong>@lang('Processed Date:')</strong> {{ $payroll->created_at }}</li>
            <li><strong>@lang('Updated Date:')</strong> {{ $payroll->updated_at }}</li>
          </ul>
        </div>
        <div class="card-footer text-center">
          <a href="{{route('payslip.pdf', $payroll->id)}}" class="btn btn-sm btn-primary mb-2 w-100">
            <i class="bx bx-download"></i> @lang('Download Payslip PDF')
          </a>
        </div>
      </div>
    </div>
  </div>
@endsection

@section('page-script')
  <style>
    @media print {
      body * {
        visibility: hidden;
      }

      #printableArea, #printableArea * {
        visibility: visible;
      }

      #printableArea {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }

      .print-hidden {
        display: none !important;
      }
    }
  </style>

  <script>
    function printPayroll() {
      window.print();
    }

    function sendPayroll() {
      Swal.fire({
        title: '@lang("Send Payroll Details")',
        text: '@lang("Are you sure you want to send payroll details to the user?")',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '@lang("Yes, send it!")',
        cancelButtonText: '@lang("Cancel")',
        customClass: {
          confirmButton: 'btn btn-primary me-2',
          cancelButton: 'btn btn-label-secondary'
        }
      }).then((result) => {
        if (result.isConfirmed) {
          Swal.fire({
            title: '@lang("Sent!")',
            text: '@lang("Payroll details have been sent successfully.")',
            icon: 'success',
            customClass: {
              confirmButton: 'btn btn-success'
            }
          });
        }
      });
    }
  </script>
@endsection
