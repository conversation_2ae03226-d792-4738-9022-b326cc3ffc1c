<?php

use App\Http\Middleware\AddonCheckMiddleware;
use Illuminate\Support\Facades\Route;
use Modules\Payroll\App\Http\Controllers\PayrollController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['middleware' => function ($request, $next) {
  $request->headers->set('addon', ModuleConstants::PAYROLL);
  return $next($request);
}], function () {
  Route::middleware([
    'web',
    'auth',
  ])->group(function () {
    Route::group([], function () {
      Route::get('payroll', [PayrollController::class, 'index'])->name('payroll.index');
      Route::get('payroll/getListAjax', [PayrollController::class, 'getListAjax'])->name('payroll.getListAjax');
      Route::get('payroll/payslip/{id}', [PayrollController::class, 'getPayslip'])->name('payroll.payslip');
      Route::get('payroll/create', [PayrollController::class, 'create'])->name('payroll.create');
      Route::get('payroll/show/{id}', [PayrollController::class, 'show'])->name('payroll.show');
      Route::get('payroll/{id}/pdf', [PayrollController::class, 'generatePayslipPDF'])
        ->name('payslip.pdf');
    });
  });
});
